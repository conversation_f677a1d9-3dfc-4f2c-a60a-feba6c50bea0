#!/usr/bin/env python3
"""
Test Virtual Environment Management for AdhocLog SharePoint Deployment

This test script validates the virtual environment management functionality
implemented for Task 4.3 to prevent multi-user conflicts in SharePoint.
"""

import os
import sys
import tempfile
import shutil
import platform
import subprocess
import getpass
from pathlib import Path
import json


class VirtualEnvironmentTest:
    """Test class for virtual environment management."""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
        self.original_dir = os.getcwd()
        
    def log_test(self, test_name, passed, message):
        """Log test result."""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status}: {test_name}")
        if message:
            print(f"     {message}")
        
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
        
    def setup_test_environment(self):
        """Set up temporary test environment."""
        self.temp_dir = tempfile.mkdtemp(prefix="adhoclog_venv_test_")
        os.chdir(self.temp_dir)
        
        # Create minimal requirements.txt for testing
        with open("requirements.txt", "w") as f:
            f.write("Flask==2.3.3\n")
            f.write("python-dotenv==1.0.0\n")
            f.write("Werkzeug==2.3.7\n")
            
        print(f"🔧 Test environment: {self.temp_dir}")
        
    def cleanup_test_environment(self):
        """Clean up test environment."""
        os.chdir(self.original_dir)
        if self.temp_dir and Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)
            print(f"🧹 Cleaned up test environment: {self.temp_dir}")
    
    def test_platform_detection(self):
        """Test platform and architecture detection."""
        test_name = "Platform Detection"
        
        try:
            platform_os = platform.system()
            platform_arch = platform.machine()
            username = getpass.getuser()
            
            # Test that we can detect basic platform info
            valid_os = platform_os in ['Windows', 'Darwin', 'Linux']
            valid_arch = platform_arch in ['x86_64', 'AMD64', 'aarch64', 'arm64', 'i386', 'i686']
            valid_user = len(username) > 0
            
            if valid_os and valid_arch and valid_user:
                self.log_test(test_name, True, 
                    f"OS: {platform_os}, Arch: {platform_arch}, User: {username}")
            else:
                self.log_test(test_name, False, 
                    f"Invalid detection - OS: {platform_os}, Arch: {platform_arch}, User: {username}")
                    
        except Exception as e:
            self.log_test(test_name, False, f"Exception: {e}")
    
    def test_venv_directory_naming(self):
        """Test virtual environment directory naming convention."""
        test_name = "Virtual Environment Directory Naming"
        
        try:
            username = getpass.getuser()
            platform_os = platform.system()
            platform_arch = platform.machine()
            
            # Map architecture names consistently
            arch_mapping = {
                'x86_64': 'x64',
                'AMD64': 'x64',
                'aarch64': 'ARM64',
                'arm64': 'ARM64',
                'i386': 'x86',
                'i686': 'x86'
            }
            platform_arch = arch_mapping.get(platform_arch, platform_arch)
            
            expected_name = f"user_{username}_{platform_os}_{platform_arch}"
            venv_dir = Path("venvs") / expected_name
            
            # Test that the naming is consistent and includes all required components
            parts = expected_name.split('_')
            if len(parts) >= 4 and parts[0] == 'user' and parts[1] == username:
                self.log_test(test_name, True, f"Expected directory: venvs/{expected_name}")
            else:
                self.log_test(test_name, False, f"Invalid naming pattern: {expected_name}")
                
        except Exception as e:
            self.log_test(test_name, False, f"Exception: {e}")
    
    def test_python_detection(self):
        """Test Python executable detection."""
        test_name = "Python Executable Detection"
        
        try:
            python_candidates = ["python3", "python", "py"]
            python_found = None
            
            for candidate in python_candidates:
                try:
                    result = subprocess.run(
                        [candidate, "--version"],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    if "3." in result.stdout:
                        python_found = candidate
                        break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
            
            if python_found:
                version_result = subprocess.run(
                    [python_found, "--version"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                self.log_test(test_name, True, 
                    f"Found: {python_found} ({version_result.stdout.strip()})")
            else:
                self.log_test(test_name, False, "No suitable Python 3.x found")
                
        except Exception as e:
            self.log_test(test_name, False, f"Exception: {e}")
    
    def test_venv_creation(self):
        """Test virtual environment creation."""
        test_name = "Virtual Environment Creation"
        
        try:
            # Find Python
            python_cmd = None
            for candidate in ["python3", "python", "py"]:
                try:
                    result = subprocess.run(
                        [candidate, "--version"],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    if "3." in result.stdout:
                        python_cmd = candidate
                        break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
                    
            if not python_cmd:
                self.log_test(test_name, False, "No Python found for testing")
                return
            
            # Create test virtual environment
            test_venv_dir = Path("test_venv")
            if test_venv_dir.exists():
                shutil.rmtree(test_venv_dir)
                
            result = subprocess.run(
                [python_cmd, "-m", "venv", str(test_venv_dir)],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and test_venv_dir.exists():
                # Check for Python executable
                if platform.system() == "Windows":
                    python_exe = test_venv_dir / "Scripts" / "python.exe"
                else:
                    python_exe = test_venv_dir / "bin" / "python"
                    
                if python_exe.exists():
                    # Test the virtual environment Python
                    venv_result = subprocess.run(
                        [str(python_exe), "--version"],
                        capture_output=True,
                        text=True
                    )
                    
                    if venv_result.returncode == 0:
                        self.log_test(test_name, True, 
                            f"Created and tested virtual environment")
                    else:
                        self.log_test(test_name, False, 
                            "Virtual environment Python not working")
                else:
                    self.log_test(test_name, False, 
                        "Virtual environment Python executable not found")
                        
                # Cleanup
                shutil.rmtree(test_venv_dir)
            else:
                self.log_test(test_name, False, 
                    f"Failed to create virtual environment: {result.stderr}")
                
        except Exception as e:
            self.log_test(test_name, False, f"Exception: {e}")
    
    def test_requirements_installation(self):
        """Test requirements installation in virtual environment."""
        test_name = "Requirements Installation"
        
        try:
            # Find Python
            python_cmd = None
            for candidate in ["python3", "python", "py"]:
                try:
                    result = subprocess.run(
                        [candidate, "--version"],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    if "3." in result.stdout:
                        python_cmd = candidate
                        break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
                    
            if not python_cmd:
                self.log_test(test_name, False, "No Python found for testing")
                return
            
            # Create test virtual environment
            test_venv_dir = Path("test_venv_req")
            if test_venv_dir.exists():
                shutil.rmtree(test_venv_dir)
                
            subprocess.run(
                [python_cmd, "-m", "venv", str(test_venv_dir)],
                capture_output=True,
                check=True
            )
            
            # Get virtual environment Python
            if platform.system() == "Windows":
                python_exe = test_venv_dir / "Scripts" / "python.exe"
            else:
                python_exe = test_venv_dir / "bin" / "python"
            
            # Try to install a simple package
            result = subprocess.run(
                [str(python_exe), "-m", "pip", "install", "flask"],
                capture_output=True,
                text=True,
                timeout=60  # 60 second timeout for package installation
            )
            
            if result.returncode == 0:
                # Verify installation
                verify_result = subprocess.run(
                    [str(python_exe), "-c", "import flask; print(flask.__version__)"],
                    capture_output=True,
                    text=True
                )
                
                if verify_result.returncode == 0:
                    self.log_test(test_name, True, 
                        f"Successfully installed and imported Flask {verify_result.stdout.strip()}")
                else:
                    self.log_test(test_name, False, 
                        "Package installed but import failed")
            else:
                # Try with trusted hosts
                result2 = subprocess.run([
                    str(python_exe), "-m", "pip", "install",
                    "--trusted-host", "pypi.org",
                    "--trusted-host", "pypi.python.org",
                    "--trusted-host", "files.pythonhosted.org",
                    "flask"
                ], capture_output=True, text=True, timeout=60)
                
                if result2.returncode == 0:
                    self.log_test(test_name, True, 
                        "Successfully installed with trusted hosts")
                else:
                    self.log_test(test_name, False, 
                        f"Installation failed: {result.stderr[:200]}")
                        
            # Cleanup
            shutil.rmtree(test_venv_dir)
            
        except subprocess.TimeoutExpired:
            self.log_test(test_name, False, "Installation timeout (network issue?)")
        except Exception as e:
            self.log_test(test_name, False, f"Exception: {e}")
    
    def test_cross_platform_compatibility(self):
        """Test cross-platform virtual environment compatibility."""
        test_name = "Cross-Platform Compatibility"
        
        try:
            current_platform = platform.system()
            current_arch = platform.machine()
            
            # Test that we can detect incompatible virtual environments
            incompatible_platforms = []
            
            if current_platform == "Windows":
                incompatible_platforms = ["Darwin_x64", "Linux_x64"]
            elif current_platform == "Darwin":
                incompatible_platforms = ["Windows_x64", "Linux_x64"]
            else:  # Linux
                incompatible_platforms = ["Windows_x64", "Darwin_x64"]
            
            # Test architecture mapping
            arch_mapping = {
                'x86_64': 'x64',
                'AMD64': 'x64',
                'aarch64': 'ARM64',
                'arm64': 'ARM64',
                'i386': 'x86',
                'i686': 'x86'
            }
            
            mapped_arch = arch_mapping.get(current_arch, current_arch)
            
            # Verify that different architectures would be detected as incompatible
            if current_arch in arch_mapping:
                self.log_test(test_name, True, 
                    f"Platform: {current_platform}, Arch: {current_arch} -> {mapped_arch}")
            else:
                self.log_test(test_name, True, 
                    f"Platform: {current_platform}, Arch: {current_arch} (unmapped)")
                    
        except Exception as e:
            self.log_test(test_name, False, f"Exception: {e}")
    
    def test_multi_user_isolation(self):
        """Test that different users would get different virtual environments."""
        test_name = "Multi-User Isolation"
        
        try:
            current_user = getpass.getuser()
            platform_os = platform.system()
            platform_arch = platform.machine()
            
            # Map architecture
            arch_mapping = {
                'x86_64': 'x64',
                'AMD64': 'x64',
                'aarch64': 'ARM64',
                'arm64': 'ARM64',
                'i386': 'x86',
                'i686': 'x86'
            }
            platform_arch = arch_mapping.get(platform_arch, platform_arch)
            
            # Simulate different users
            test_users = ["user1", "user2", "testuser"]
            
            venv_paths = []
            for user in test_users:
                venv_name = f"user_{user}_{platform_os}_{platform_arch}"
                venv_path = Path("venvs") / venv_name
                venv_paths.append(str(venv_path))
            
            # Check that all paths are different
            unique_paths = set(venv_paths)
            
            current_venv = f"user_{current_user}_{platform_os}_{platform_arch}"
            
            if len(unique_paths) == len(venv_paths):
                self.log_test(test_name, True, 
                    f"Users get isolated virtual environments. Current: {current_venv}")
            else:
                self.log_test(test_name, False, 
                    "Virtual environment paths are not unique per user")
                    
        except Exception as e:
            self.log_test(test_name, False, f"Exception: {e}")
    
    def test_cleanup_functionality(self):
        """Test cleanup of old virtual environments."""
        test_name = "Virtual Environment Cleanup"
        
        try:
            # Create mock old virtual environment directories
            venvs_dir = Path("venvs")
            venvs_dir.mkdir(exist_ok=True)
            
            current_user = getpass.getuser()
            platform_os = platform.system()
            platform_arch = platform.machine()
            
            # Map architecture
            arch_mapping = {
                'x86_64': 'x64',
                'AMD64': 'x64',
                'aarch64': 'ARM64',
                'arm64': 'ARM64',
                'i386': 'x86',
                'i686': 'x86'
            }
            platform_arch = arch_mapping.get(platform_arch, platform_arch)
            
            current_venv = f"user_{current_user}_{platform_os}_{platform_arch}"
            
            # Create mock old environments
            old_venvs = [
                f"user_{current_user}_Windows_x64",
                f"user_{current_user}_Darwin_x64", 
                f"user_{current_user}_Linux_ARM64"
            ]
            
            created_dirs = []
            for old_venv in old_venvs:
                if old_venv != current_venv:
                    old_dir = venvs_dir / old_venv
                    old_dir.mkdir(exist_ok=True)
                    # Create a test file to make it a real directory
                    (old_dir / "test.txt").write_text("test")
                    created_dirs.append(old_dir)
            
            # Simulate cleanup - count directories that would be removed
            cleanup_count = len([d for d in created_dirs if d.exists()])
            
            # Clean up our test directories
            for test_dir in created_dirs:
                if test_dir.exists():
                    shutil.rmtree(test_dir)
            
            if cleanup_count > 0:
                self.log_test(test_name, True, 
                    f"Would clean up {cleanup_count} old virtual environments")
            else:
                self.log_test(test_name, True, 
                    "No old virtual environments to clean (expected)")
                    
        except Exception as e:
            self.log_test(test_name, False, f"Exception: {e}")
    
    def run_all_tests(self):
        """Run all virtual environment tests."""
        print("🧪 AdhocLog Virtual Environment Management Tests")
        print("=" * 60)
        print()
        
        # Set up test environment
        try:
            self.setup_test_environment()
            
            # Run tests
            self.test_platform_detection()
            self.test_venv_directory_naming()
            self.test_python_detection()
            self.test_venv_creation()
            self.test_requirements_installation()
            self.test_cross_platform_compatibility()
            self.test_multi_user_isolation()
            self.test_cleanup_functionality()
            
        finally:
            self.cleanup_test_environment()
        
        # Summary
        print()
        print("=" * 60)
        print("📊 Test Summary")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print()
            print("❌ Failed Tests:")
            for result in self.test_results:
                if not result['passed']:
                    print(f"  • {result['test']}: {result['message']}")
        
        return failed_tests == 0


def main():
    """Main test entry point."""
    try:
        tester = VirtualEnvironmentTest()
        success = tester.run_all_tests()
        
        if success:
            print("\n🎉 All virtual environment tests passed!")
            print("✅ Virtual environment management is ready for SharePoint deployment")
        else:
            print("\n⚠️ Some tests failed - review implementation")
            
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test framework error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
