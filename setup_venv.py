#!/usr/bin/env python3
"""
AdhocLog Virtual Environment Setup Utility

This utility helps create and manage user-specific virtual environments
for SharePoint deployments with cross-platform compatibility.
"""

import os
import sys
import platform
import subprocess
import shutil
import getpass
from pathlib import Path


class VirtualEnvironmentManager:
    """Manages user-specific virtual environments for AdhocLog."""

    def __init__(self):
        self.username = getpass.getuser()
        self.platform_os = platform.system()
        self.platform_arch = platform.machine()

        # Map platform.machine() output to consistent naming
        arch_mapping = {
            'x86_64': 'x64',
            'AMD64': 'x64',
            'aarch64': 'ARM64',
            'arm64': 'ARM64',
            'i386': 'x86',
            'i686': 'x86'
        }
        self.platform_arch = arch_mapping.get(self.platform_arch, self.platform_arch)

        self.venv_name = f"user_{self.username}_{self.platform_os}_{self.platform_arch}"
        self.venv_dir = Path("venvs") / self.venv_name

    def detect_python_executable(self):
        """Detect the best Python executable to use."""
        python_candidates = [
            "python3", "python", "py -3",
            "python3.11", "python3.10", "python3.9", "python3.8", "python3.7"
        ]

        for candidate in python_candidates:
            try:
                result = subprocess.run(
                    candidate.split() + ["--version"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                version = result.stdout.strip()
                if "3." in version:
                    print(f"✅ Found Python: {candidate} ({version})")
                    return candidate.split()[0] if " " not in candidate else candidate
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue

        raise RuntimeError("No suitable Python 3.7+ installation found")

    def check_existing_venv(self):
        """Check if existing virtual environment is valid and compatible."""
        if not self.venv_dir.exists():
            return False, "Virtual environment directory does not exist"

        # Check for Python executable
        if self.platform_os == "Windows":
            python_exe = self.venv_dir / "Scripts" / "python.exe"
        else:
            python_exe = self.venv_dir / "bin" / "python"

        if not python_exe.exists():
            return False, "Python executable not found in virtual environment"

        try:
            # Test if Python works
            result = subprocess.run(
                [str(python_exe), "--version"],
                capture_output=True,
                text=True,
                check=True
            )

            # Check platform compatibility
            result_platform = subprocess.run(
                [str(python_exe), "-c", "import platform; print(platform.machine())"],
                capture_output=True,
                text=True,
                check=True
            )

            venv_arch = result_platform.stdout.strip()
            # Map to consistent naming
            arch_mapping = {
                'x86_64': 'x64',
                'AMD64': 'x64',
                'aarch64': 'ARM64',
                'arm64': 'ARM64',
                'i386': 'x86',
                'i686': 'x86'
            }
            venv_arch = arch_mapping.get(venv_arch, venv_arch)

            if venv_arch != self.platform_arch:
                return False, f"Architecture mismatch: {venv_arch} != {self.platform_arch}"

            # Check if requirements are installed
            try:
                subprocess.run(
                    [str(python_exe), "-c", "import flask"],
                    capture_output=True,
                    check=True
                )
                return True, "Virtual environment is valid and requirements are installed"
            except subprocess.CalledProcessError:
                return True, "Virtual environment is valid but requirements need installation"

        except subprocess.CalledProcessError as e:
            return False, f"Virtual environment Python is broken: {e}"

    def create_venv(self, python_cmd):
        """Create a new virtual environment."""
        print(f"📦 Creating virtual environment: {self.venv_dir}")

        # Remove existing broken venv if it exists
        if self.venv_dir.exists():
            print("🗑️ Removing existing broken virtual environment...")
            shutil.rmtree(self.venv_dir)

        # Create parent directory
        self.venv_dir.parent.mkdir(exist_ok=True)

        try:
            subprocess.run(
                [python_cmd, "-m", "venv", str(self.venv_dir)],
                check=True
            )
            print("✅ Virtual environment created successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            return False

    def install_requirements(self):
        """Install requirements in the virtual environment."""
        if self.platform_os == "Windows":
            python_exe = self.venv_dir / "Scripts" / "python.exe"
            pip_exe = self.venv_dir / "Scripts" / "pip.exe"
        else:
            python_exe = self.venv_dir / "bin" / "python"
            pip_exe = self.venv_dir / "bin" / "pip"

        print("📦 Installing requirements...")

        # Upgrade pip first
        try:
            subprocess.run(
                [str(python_exe), "-m", "pip", "install", "--upgrade", "pip"],
                check=True,
                capture_output=True
            )
            print("✅ pip upgraded successfully")
        except subprocess.CalledProcessError:
            print("⚠️ pip upgrade failed, continuing with existing version")

        # Install requirements
        requirements_file = Path("requirements.txt")
        if not requirements_file.exists():
            print("⚠️ requirements.txt not found")
            return False

        try:
            subprocess.run(
                [str(python_exe), "-m", "pip", "install", "-r", "requirements.txt"],
                check=True,
                capture_output=True
            )
            print("✅ Requirements installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("⚠️ Standard installation failed, trying with trusted hosts...")
            try:
                subprocess.run([
                    str(python_exe), "-m", "pip", "install",
                    "--trusted-host", "pypi.org",
                    "--trusted-host", "pypi.python.org",
                    "--trusted-host", "files.pythonhosted.org",
                    "-r", "requirements.txt"
                ], check=True, capture_output=True)
                print("✅ Requirements installed with trusted hosts")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install requirements: {e}")
                return False

    def cleanup_old_venvs(self):
        """Remove old virtual environments for the same user but different platforms."""
        venvs_dir = Path("venvs")
        if not venvs_dir.exists():
            return

        current_venv = self.venv_name
        user_prefix = f"user_{self.username}_"

        print("🧹 Cleaning up old virtual environments...")
        cleaned = 0

        for venv_path in venvs_dir.iterdir():
            if (venv_path.is_dir() and
                venv_path.name.startswith(user_prefix) and
                venv_path.name != current_venv):

                print(f"🗑️ Removing old virtual environment: {venv_path.name}")
                try:
                    shutil.rmtree(venv_path)
                    cleaned += 1
                except OSError as e:
                    print(f"⚠️ Could not remove {venv_path.name}: {e}")

        if cleaned > 0:
            print(f"✅ Cleaned up {cleaned} old virtual environment(s)")
        else:
            print("✅ No old virtual environments to clean")

    def setup(self):
        """Main setup function."""
        print("=" * 50)
        print("🔧 AdhocLog Virtual Environment Setup")
        print("=" * 50)
        print(f"👤 User: {self.username}")
        print(f"🖥️ Platform: {self.platform_os} {self.platform_arch}")
        print(f"📁 Virtual Environment: {self.venv_dir}")
        print()

        # Check existing virtual environment
        is_valid, reason = self.check_existing_venv()
        if is_valid:
            print(f"✅ {reason}")
            if "requirements need installation" in reason:
                if not self.install_requirements():
                    return False
            self.cleanup_old_venvs()
            print("🎉 Virtual environment setup complete!")
            return True

        print(f"🔍 {reason}")

        # Detect Python
        try:
            python_cmd = self.detect_python_executable()
        except RuntimeError as e:
            print(f"❌ {e}")
            return False

        # Create virtual environment
        if not self.create_venv(python_cmd):
            return False

        # Install requirements
        if not self.install_requirements():
            return False

        # Cleanup old environments
        self.cleanup_old_venvs()

        print("🎉 Virtual environment setup complete!")
        return True


def main():
    """Main entry point."""
    try:
        manager = VirtualEnvironmentManager()
        success = manager.setup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
