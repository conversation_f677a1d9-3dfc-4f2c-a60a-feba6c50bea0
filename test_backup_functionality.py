#!/usr/bin/env python3
"""
Test script for backup and restore functionality
"""

import os
import json
import shutil
from pathlib import Path
from datetime import datetime

def create_test_data():
    """Create some test data files for backup testing"""
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)

    # Create sample tasks file
    sample_tasks = [
        {
            "id": 1,
            "date": "2025-01-28",
            "team_member": "test_user",
            "title": "Test Task 1",
            "classification": "Planning",
            "category": "Adhoc",
            "description": "This is a test task for backup testing",
            "est_time": 30,
            "completed": False
        },
        {
            "id": 2,
            "date": "2025-01-28",
            "team_member": "test_user",
            "title": "Test Task 2",
            "classification": "Execution",
            "category": "Adhoc",
            "description": "Another test task",
            "est_time": 45,
            "completed": True
        }
    ]

    # Write tasks file
    tasks_file = data_dir / "tasks_test_user.json"
    with open(tasks_file, 'w', encoding='utf-8') as f:
        json.dump(sample_tasks, f, indent=2)

    # Create sample archived tasks
    archived_tasks = [
        {
            "id": 3,
            "date": "2025-01-27",
            "team_member": "test_user",
            "title": "Archived Test Task",
            "classification": "Business Support Activities",
            "category": "Business Support Activities",
            "description": "This task was archived",
            "est_time": 60,
            "completed": True,
            "archived": True,
            "archived_date": "2025-01-28"
        }
    ]

    archived_file = data_dir / "archived_tasks_test_user.json"
    with open(archived_file, 'w', encoding='utf-8') as f:
        json.dump(archived_tasks, f, indent=2)

    # Create analytics file
    analytics_data = {
        "last_updated": datetime.now().isoformat(),
        "total_tasks_created": 3,
        "total_time_logged": 135
    }

    analytics_file = data_dir / "analytics.json"
    with open(analytics_file, 'w', encoding='utf-8') as f:
        json.dump(analytics_data, f, indent=2)

    print("✅ Created test data files:")
    print(f"  • {tasks_file}")
    print(f"  • {archived_file}")
    print(f"  • {analytics_file}")

def test_backup_creation():
    """Test manual backup creation (simulating GUI functionality)"""
    data_path = Path("data")

    if not data_path.exists():
        print("❌ No data directory found. Run create_test_data() first.")
        return False

    # Get all JSON files in data directory
    data_files = list(data_path.glob("*.json"))

    if not data_files:
        print("❌ No data files found for backup.")
        return False

    # Create backup
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    backup_root = Path("backups")
    backup_dir = backup_root / f"backup_{timestamp}"

    backup_root.mkdir(exist_ok=True)
    backup_dir.mkdir(exist_ok=True)

    print(f"📁 Creating backup directory: {backup_dir}")

    # Copy files
    copied_count = 0
    for data_file in data_files:
        try:
            destination = backup_dir / data_file.name
            shutil.copy2(data_file, destination)
            copied_count += 1
            print(f"💾 Backed up: {data_file.name}")
        except Exception as e:
            print(f"⚠️ Failed to backup {data_file.name}: {e}")

    # Create backup info
    info_file = backup_dir / "backup_info.txt"
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(f"AdhocLog Data Backup\n")
        f.write(f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Files backed up: {copied_count}\n")
        f.write(f"Source directory: {data_path.absolute()}\n\n")
        f.write("Files included:\n")
        for data_file in data_files:
            if (backup_dir / data_file.name).exists():
                f.write(f"  ✅ {data_file.name}\n")
            else:
                f.write(f"  ❌ {data_file.name} (failed to copy)\n")

    print(f"✅ Backup created successfully!")
    print(f"📊 Files backed up: {copied_count} out of {len(data_files)}")
    print(f"📁 Backup location: {backup_dir}")

    return backup_dir

def test_backup_restore(backup_dir):
    """Test restore functionality"""
    if not backup_dir or not backup_dir.exists():
        print("❌ Invalid backup directory")
        return False

    print(f"🔄 Testing restore from: {backup_dir}")

    # Simulate clearing current data
    data_path = Path("data")
    if data_path.exists():
        # Backup current data before clearing
        current_files = list(data_path.glob("*.json"))
        temp_backup = Path("temp_current_backup")
        temp_backup.mkdir(exist_ok=True)

        for file in current_files:
            shutil.copy2(file, temp_backup / file.name)

        # Clear current data
        for file in current_files:
            file.unlink()

        print(f"🗑️ Cleared {len(current_files)} current data files")

    # Restore from backup
    backup_files = list(backup_dir.glob("*.json"))
    data_path.mkdir(exist_ok=True)

    restored_count = 0
    for backup_file in backup_files:
        try:
            destination = data_path / backup_file.name
            shutil.copy2(backup_file, destination)
            restored_count += 1
            print(f"🔄 Restored: {backup_file.name}")
        except Exception as e:
            print(f"⚠️ Failed to restore {backup_file.name}: {e}")

    print(f"✅ Restore completed!")
    print(f"📊 Files restored: {restored_count} out of {len(backup_files)}")

    # Verify restored data
    restored_files = list(data_path.glob("*.json"))
    print(f"📋 Verified {len(restored_files)} files in data directory")

    return True

def list_backups():
    """List available backups"""
    backup_root = Path("backups")

    if not backup_root.exists():
        print("ℹ️ No backups directory found")
        return []

    backup_dirs = [d for d in backup_root.iterdir() if d.is_dir() and d.name.startswith("backup_")]

    if not backup_dirs:
        print("ℹ️ No backup directories found")
        return []

    backup_dirs.sort(key=lambda x: x.name, reverse=True)

    print("📋 Available backups:")
    for i, backup_dir in enumerate(backup_dirs, 1):
        timestamp_str = backup_dir.name.replace("backup_", "").replace("_", " at ").replace("-", "/")
        file_count = len(list(backup_dir.glob("*.json")))
        print(f"  {i}. {timestamp_str} ({file_count} files)")

    return backup_dirs

def main():
    """Main test function"""
    print("🧪 AdhocLog Backup & Restore Test")
    print("=" * 40)

    print("\n1. Creating test data...")
    create_test_data()

    print("\n2. Listing current data files...")
    data_files = list(Path("data").glob("*.json"))
    print(f"📊 Found {len(data_files)} data files")

    print("\n3. Creating backup...")
    backup_dir = test_backup_creation()

    print("\n4. Listing available backups...")
    available_backups = list_backups()

    print("\n5. Testing restore...")
    if backup_dir:
        test_backup_restore(backup_dir)

    print("\n6. Final verification...")
    final_files = list(Path("data").glob("*.json"))
    print(f"📊 Final data files: {len(final_files)}")

    print("\n✅ Backup and restore test completed!")
    print("\nYou can now test the GUI backup/restore buttons:")
    print("  • Run: python gui_launcher.py")
    print("  • Click 'Backup Data' to create a backup")
    print("  • Click 'Restore Data' to restore from a backup")

if __name__ == "__main__":
    main()
